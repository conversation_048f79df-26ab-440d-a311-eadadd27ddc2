# Vertex AI Setup Guide

This guide will help you set up Vertex AI for the Excel AI Summarizer.

## Prerequisites

1. **Google Cloud Platform Account**: You need a GCP account with billing enabled
2. **GCP Project**: Create or use an existing GCP project
3. **Vertex AI API**: Enable the Vertex AI API in your project

## Step-by-Step Setup

### 1. Create/Select a GCP Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your **Project ID** (you'll need this later)

### 2. Enable Vertex AI API

1. In the GCP Console, go to **APIs & Services > Library**
2. Search for "Vertex AI API"
3. Click on it and press **Enable**

### 3. Set up Authentication

You have two options for authentication:

#### Option A: Service Account Key (Recommended for local development)

1. Go to **IAM & Admin > Service Accounts**
2. Click **Create Service Account**
3. Give it a name (e.g., "excel-ai-summarizer")
4. Grant the following roles:
   - **Vertex AI User**
   - **AI Platform Developer** (if available)
5. Click **Done**
6. Click on the created service account
7. Go to **Keys** tab
8. Click **Add Key > Create New Key**
9. Choose **JSON** format
10. Download the key file
11. Save it securely (e.g., `~/gcp-credentials.json`)

#### Option B: Application Default Credentials (For production)

1. Install Google Cloud CLI: https://cloud.google.com/sdk/docs/install
2. Run: `gcloud auth application-default login`
3. Follow the authentication flow

### 4. Configure Environment Variables

Edit your `.env` file:

```env
# Vertex AI Configuration
VERTEX_AI_PROJECT_ID=your-gcp-project-id
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL=gemini-1.5-flash

# If using service account key (Option A)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json

# MCP Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8000

# Application Configuration
DEFAULT_PROMPT="Please provide a comprehensive summary of the data from all sheets in this Excel file, highlighting key insights, trends, and important information."
MAX_TOKENS=4000
TEMPERATURE=0.7
```

### 5. Available Regions

Choose a region close to you for better performance:

- **us-central1** (Iowa, USA)
- **us-east1** (South Carolina, USA)
- **us-west1** (Oregon, USA)
- **europe-west1** (Belgium)
- **europe-west4** (Netherlands)
- **asia-southeast1** (Singapore)

### 6. Available Models

- **gemini-1.5-flash**: Fast, efficient model (recommended)
- **gemini-1.5-pro**: More capable but slower
- **gemini-1.0-pro**: Previous generation

## Testing Your Setup

1. **Test configuration**:
   ```bash
   python3 test_workflow.py
   ```

2. **Test AI client directly**:
   ```bash
   python3 google_ai_client.py
   ```

3. **Run interactive mode**:
   ```bash
   python3 main.py
   ```

## Usage Examples

### Command Line with Vertex AI

```bash
# Using environment variables
python3 main.py --mode cli --file aws-inventory.xlsx

# Explicit Vertex AI configuration
python3 main.py --mode cli --file aws-inventory.xlsx --use-vertex-ai --project-id your-project-id --location us-central1
```

### Server Mode

```bash
python3 main.py --mode server
```

## Troubleshooting

### Common Issues

1. **"Project not found" error**:
   - Verify your project ID is correct
   - Ensure you have access to the project

2. **"Permission denied" error**:
   - Check that Vertex AI API is enabled
   - Verify your service account has the correct roles

3. **"Credentials not found" error**:
   - Ensure `GOOGLE_APPLICATION_CREDENTIALS` points to the correct file
   - Or run `gcloud auth application-default login`

4. **"Region not supported" error**:
   - Use a supported region from the list above
   - Check if the model is available in your chosen region

### Checking Your Setup

```bash
# Check if you're authenticated
gcloud auth list

# Check your current project
gcloud config get-value project

# Test Vertex AI access
gcloud ai models list --region=us-central1
```

## Cost Considerations

- Vertex AI charges per token (input + output)
- Gemini 1.5 Flash: ~$0.075 per 1M input tokens, ~$0.30 per 1M output tokens
- Monitor usage in GCP Console > Billing

## Security Best Practices

1. **Limit service account permissions** to only what's needed
2. **Rotate service account keys** regularly
3. **Use IAM conditions** to restrict access by IP/time if needed
4. **Monitor API usage** for unexpected spikes
5. **Store credentials securely** and never commit them to version control

## Switching Between AI Studio and Vertex AI

The application automatically detects which service to use:

- **Vertex AI**: If `VERTEX_AI_PROJECT_ID` is set
- **AI Studio**: If `GOOGLE_API_KEY` is set and no project ID

You can also force the choice:
```bash
# Force Vertex AI
python3 main.py --use-vertex-ai --project-id your-project-id

# Force AI Studio (if you have both configured)
python3 main.py --api-key your-api-key
```
