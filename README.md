# Excel AI Summarizer

An AI-powered tool that processes Excel files with multiple sheets and generates comprehensive summaries using Google's Generative AI and MCP (Model Context Protocol) server.

## 🚀 Quick Start

Run the startup script for an interactive setup:
```bash
./start.sh
```

Or manually:
```bash
python3 main.py
```

## ✨ Features

- **Multi-sheet Excel Processing**: Automatically reads and processes all sheets in an Excel file
- **AI-Powered Summaries**: Uses Google's Generative AI to create intelligent summaries
- **MCP Server**: Provides a REST API for integration with other applications
- **Multiple Interfaces**: CLI, interactive mode, and web API
- **Customizable Prompts**: Support for custom AI prompts
- **Flexible Configuration**: Environment-based configuration management

## 📦 Installation

1. **Dependencies are already installed** (pandas, google-generativeai, fastapi, etc.)

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and add your Google AI API key:
   ```
   GOOGLE_API_KEY=your_google_ai_api_key_here
   ```

3. **Test the setup**:
   ```bash
   python3 test_workflow.py
   ```

## AI Service Setup

Choose one of the following options:

### Option 1: Vertex AI (Recommended for production)

1. **Follow the detailed setup guide**: See `VERTEX_AI_SETUP.md`
2. **Set up GCP project and authentication**
3. **Configure environment variables**:
   ```env
   VERTEX_AI_PROJECT_ID=your-gcp-project-id
   VERTEX_AI_LOCATION=us-central1
   GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
   ```

### Option 2: Google AI Studio (Easier for testing)

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file:
   ```env
   GOOGLE_API_KEY=your_google_ai_api_key_here
   ```

## Usage

### 1. Interactive Mode (Default)
```bash
python main.py
```
This starts an interactive session where you can process Excel files step by step.

### 2. CLI Mode
```bash
python main.py --mode cli --file aws-inventory.xlsx --output summary.txt
```

### 3. MCP Server Mode
```bash
python main.py --mode server --host localhost --port 8000
```

### 4. Using Vertex AI
```bash
python main.py --use-vertex-ai --project-id your-project-id
```

### 5. Custom Prompt
```bash
python main.py --mode cli --file aws-inventory.xlsx --prompt "Analyze this AWS inventory data and provide cost optimization recommendations"
```

## API Endpoints (Server Mode)

When running in server mode, the following endpoints are available:

- `GET /` - Server status
- `GET /health` - Health check
- `POST /upload-and-summarize` - Upload and process Excel file
- `POST /summarize-local-file` - Process local Excel file
- `POST /custom-analysis` - Generate custom analysis
- `GET /file-info/{file_path}` - Get Excel file information

### Example API Usage

```bash
# Health check
curl http://localhost:8000/health

# Summarize local file
curl -X POST "http://localhost:8000/summarize-local-file" \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "aws-inventory.xlsx",
    "custom_prompt": "Provide a detailed analysis of AWS resources",
    "max_rows_per_sheet": 100
  }'
```

## Configuration

The application can be configured through environment variables in the `.env` file:

```env
# Google AI API Configuration
GOOGLE_API_KEY=your_api_key_here
GOOGLE_MODEL_NAME=gemini-1.5-flash

# Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8000

# Processing Configuration
MAX_ROWS_PER_SHEET=100
MAX_TOKENS=4000
TEMPERATURE=0.7

# Default prompt
DEFAULT_PROMPT="Please provide a comprehensive summary..."
```

## Project Structure

```
excel-ai-summarizer/
├── main.py                 # Main application entry point
├── excel_processor.py      # Excel file processing module
├── google_ai_client.py     # Google AI integration
├── mcp_server.py          # MCP server implementation
├── config.py              # Configuration management
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── .env                  # Your environment variables (create this)
└── README.md             # This file
```

## Example Output

The AI will generate summaries like:

```
EXCEL FILE ANALYSIS: aws-inventory.xlsx
==================================================

## Overview
This Excel file contains AWS infrastructure inventory data across 3 sheets...

## Key Insights
1. **Resource Distribution**: The majority of resources are concentrated in us-east-1...
2. **Cost Analysis**: EC2 instances represent 65% of total infrastructure costs...
3. **Security Findings**: 12 security groups have overly permissive rules...

## Recommendations
1. Consider consolidating resources in fewer regions...
2. Review and optimize underutilized EC2 instances...
```

## Error Handling

The application includes comprehensive error handling for:
- Missing or invalid Excel files
- API key issues
- Network connectivity problems
- File processing errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:
1. Check the error messages and logs
2. Verify your Google AI API key is valid
3. Ensure Excel files are in supported formats (.xlsx, .xls)
4. Check network connectivity for API calls
