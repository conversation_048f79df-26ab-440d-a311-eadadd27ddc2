"""
Configuration module for Excel AI Summarizer
Handles application settings and environment variables.
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class AIConfig:
    """Configuration for AI client."""
    # Vertex AI settings
    project_id: Optional[str] = None
    location: str = "us-central1"
    use_vertex_ai: bool = False

    # Google AI Studio settings
    api_key: Optional[str] = None

    # Common settings
    model_name: str = "gemini-1.5-flash"
    max_tokens: int = 4000
    temperature: float = 0.7
    default_prompt: str = (
        "Please provide a comprehensive summary of the data from all sheets in this Excel file, "
        "highlighting key insights, trends, and important information."
    )


@dataclass
class ServerConfig:
    """Configuration for MCP server."""
    host: str = "localhost"
    port: int = 8000
    debug: bool = False


@dataclass
class ProcessingConfig:
    """Configuration for Excel processing."""
    max_rows_per_sheet: int = 100
    supported_formats: tuple = ('.xlsx', '.xls')
    temp_dir: Optional[str] = None


@dataclass
class AppConfig:
    """Main application configuration."""
    ai: AIConfig
    server: ServerConfig
    processing: ProcessingConfig
    log_level: str = "INFO"


def load_config() -> AppConfig:
    """
    Load configuration from environment variables and defaults.

    Returns:
        AppConfig: Application configuration
    """
    # Determine which AI service to use
    project_id = os.getenv("VERTEX_AI_PROJECT_ID")
    api_key = os.getenv("GOOGLE_API_KEY")
    use_vertex_ai = bool(project_id)

    # Validate configuration
    if not use_vertex_ai and not api_key:
        raise ValueError(
            "Either VERTEX_AI_PROJECT_ID (for Vertex AI) or GOOGLE_API_KEY (for AI Studio) is required"
        )

    ai_config = AIConfig(
        project_id=project_id,
        location=os.getenv("VERTEX_AI_LOCATION", "us-central1"),
        use_vertex_ai=use_vertex_ai,
        api_key=api_key,
        model_name=os.getenv("VERTEX_AI_MODEL", "gemini-1.5-flash"),
        max_tokens=int(os.getenv("MAX_TOKENS", "4000")),
        temperature=float(os.getenv("TEMPERATURE", "0.7")),
        default_prompt=os.getenv("DEFAULT_PROMPT", AIConfig.default_prompt)
    )
    
    # Server Configuration
    server_config = ServerConfig(
        host=os.getenv("MCP_SERVER_HOST", "localhost"),
        port=int(os.getenv("MCP_SERVER_PORT", "8000")),
        debug=os.getenv("DEBUG", "false").lower() == "true"
    )
    
    # Processing Configuration
    processing_config = ProcessingConfig(
        max_rows_per_sheet=int(os.getenv("MAX_ROWS_PER_SHEET", "100")),
        temp_dir=os.getenv("TEMP_DIR")
    )
    
    return AppConfig(
        ai=ai_config,
        server=server_config,
        processing=processing_config,
        log_level=os.getenv("LOG_LEVEL", "INFO")
    )


def get_config_dict() -> Dict[str, Any]:
    """
    Get configuration as a dictionary.

    Returns:
        Dict[str, Any]: Configuration dictionary
    """
    try:
        config = load_config()
        return {
            "ai": {
                "service": "Vertex AI" if config.ai.use_vertex_ai else "Google AI Studio",
                "model_name": config.ai.model_name,
                "max_tokens": config.ai.max_tokens,
                "temperature": config.ai.temperature,
                "project_id": config.ai.project_id if config.ai.use_vertex_ai else None,
                "location": config.ai.location if config.ai.use_vertex_ai else None,
                "has_api_key": bool(config.ai.api_key) if not config.ai.use_vertex_ai else None,
                "use_vertex_ai": config.ai.use_vertex_ai
            },
            "server": {
                "host": config.server.host,
                "port": config.server.port,
                "debug": config.server.debug
            },
            "processing": {
                "max_rows_per_sheet": config.processing.max_rows_per_sheet,
                "supported_formats": config.processing.supported_formats,
                "temp_dir": config.processing.temp_dir
            },
            "log_level": config.log_level
        }
    except ValueError as e:
        # Return basic structure if configuration is incomplete
        return {
            "ai": {
                "service": "Not configured",
                "error": str(e),
                "use_vertex_ai": bool(os.getenv("VERTEX_AI_PROJECT_ID")),
                "has_api_key": bool(os.getenv("GOOGLE_API_KEY"))
            },
            "server": {"host": "localhost", "port": 8000},
            "processing": {"max_rows_per_sheet": 100}
        }


# Global configuration instance
_config: Optional[AppConfig] = None


def get_config() -> AppConfig:
    """
    Get the global configuration instance.
    
    Returns:
        AppConfig: Application configuration
    """
    global _config
    if _config is None:
        _config = load_config()
    return _config


def reload_config() -> AppConfig:
    """
    Reload configuration from environment variables.
    
    Returns:
        AppConfig: Reloaded application configuration
    """
    global _config
    load_dotenv(override=True)  # Reload .env file
    _config = load_config()
    return _config


if __name__ == "__main__":
    # Test configuration loading
    try:
        config = load_config()
        print("Configuration loaded successfully:")
        print(f"AI Model: {config.ai.model_name}")
        print(f"Server: {config.server.host}:{config.server.port}")
        print(f"Max rows per sheet: {config.processing.max_rows_per_sheet}")
        print(f"Log level: {config.log_level}")
    except Exception as e:
        print(f"Error loading configuration: {e}")
