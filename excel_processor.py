"""
Excel File Processing Module
Handles reading Excel files with multiple sheets and preparing data for AI processing.
"""

import pandas as pd
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ExcelProcessor:
    """Class to handle Excel file processing and data extraction."""
    
    def __init__(self, file_path: str):
        """
        Initialize the Excel processor.
        
        Args:
            file_path (str): Path to the Excel file
        """
        self.file_path = Path(file_path)
        self.data = {}
        self.sheet_names = []
        
        if not self.file_path.exists():
            raise FileNotFoundError(f"Excel file not found: {file_path}")
    
    def read_excel_file(self) -> Dict[str, pd.DataFrame]:
        """
        Read all sheets from the Excel file.
        
        Returns:
            Dict[str, pd.DataFrame]: Dictionary with sheet names as keys and DataFrames as values
        """
        try:
            logger.info(f"Reading Excel file: {self.file_path}")
            
            # Read all sheets
            excel_data = pd.read_excel(self.file_path, sheet_name=None, engine='openpyxl')
            self.data = excel_data
            self.sheet_names = list(excel_data.keys())
            
            logger.info(f"Successfully read {len(self.sheet_names)} sheets: {self.sheet_names}")
            return excel_data
            
        except Exception as e:
            logger.error(f"Error reading Excel file: {str(e)}")
            raise
    
    def get_sheet_summary(self, sheet_name: str) -> Dict[str, Any]:
        """
        Get summary information about a specific sheet.
        
        Args:
            sheet_name (str): Name of the sheet
            
        Returns:
            Dict[str, Any]: Summary information about the sheet
        """
        if sheet_name not in self.data:
            raise ValueError(f"Sheet '{sheet_name}' not found in the Excel file")
        
        df = self.data[sheet_name]
        
        summary = {
            "sheet_name": sheet_name,
            "rows": len(df),
            "columns": len(df.columns),
            "column_names": df.columns.tolist(),
            "data_types": df.dtypes.to_dict(),
            "null_counts": df.isnull().sum().to_dict(),
            "sample_data": df.head(3).to_dict('records') if len(df) > 0 else []
        }
        
        return summary
    
    def get_all_sheets_summary(self) -> Dict[str, Any]:
        """
        Get summary information for all sheets.
        
        Returns:
            Dict[str, Any]: Summary information for all sheets
        """
        if not self.data:
            self.read_excel_file()
        
        all_summaries = {}
        for sheet_name in self.sheet_names:
            all_summaries[sheet_name] = self.get_sheet_summary(sheet_name)
        
        return all_summaries
    
    def convert_to_text_format(self, max_rows_per_sheet: int = 100) -> str:
        """
        Convert Excel data to a text format suitable for AI processing.
        
        Args:
            max_rows_per_sheet (int): Maximum number of rows to include per sheet
            
        Returns:
            str: Formatted text representation of the Excel data
        """
        if not self.data:
            self.read_excel_file()
        
        text_output = []
        text_output.append(f"EXCEL FILE ANALYSIS: {self.file_path.name}")
        text_output.append("=" * 50)
        text_output.append(f"Total Sheets: {len(self.sheet_names)}")
        text_output.append(f"Sheet Names: {', '.join(self.sheet_names)}")
        text_output.append("")
        
        for sheet_name in self.sheet_names:
            df = self.data[sheet_name]
            text_output.append(f"SHEET: {sheet_name}")
            text_output.append("-" * 30)
            text_output.append(f"Dimensions: {len(df)} rows × {len(df.columns)} columns")
            text_output.append(f"Columns: {', '.join(df.columns.tolist())}")
            text_output.append("")
            
            # Add sample data
            if len(df) > 0:
                text_output.append("Sample Data:")
                # Limit the number of rows to prevent overwhelming the AI
                sample_df = df.head(min(max_rows_per_sheet, len(df)))
                text_output.append(sample_df.to_string(index=False))
            else:
                text_output.append("No data in this sheet.")
            
            text_output.append("")
            text_output.append("")
        
        return "\n".join(text_output)
    
    def get_sheet_data_as_json(self, sheet_name: str, max_rows: int = 100) -> str:
        """
        Get sheet data as JSON string.
        
        Args:
            sheet_name (str): Name of the sheet
            max_rows (int): Maximum number of rows to include
            
        Returns:
            str: JSON representation of the sheet data
        """
        if sheet_name not in self.data:
            raise ValueError(f"Sheet '{sheet_name}' not found in the Excel file")
        
        df = self.data[sheet_name]
        limited_df = df.head(max_rows)
        
        # Convert to JSON, handling NaN values
        return limited_df.fillna("").to_json(orient='records', indent=2)


def process_excel_file(file_path: str, max_rows_per_sheet: int = 100) -> str:
    """
    Convenience function to process an Excel file and return formatted text.
    
    Args:
        file_path (str): Path to the Excel file
        max_rows_per_sheet (int): Maximum number of rows to include per sheet
        
    Returns:
        str: Formatted text representation of the Excel data
    """
    processor = ExcelProcessor(file_path)
    return processor.convert_to_text_format(max_rows_per_sheet)


if __name__ == "__main__":
    # Test the processor with the existing Excel file
    try:
        processor = ExcelProcessor("aws-inventory.xlsx")
        text_data = processor.convert_to_text_format()
        print(text_data)
    except Exception as e:
        print(f"Error: {e}")
