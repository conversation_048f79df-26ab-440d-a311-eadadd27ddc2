"""
MCP (Model Context Protocol) Server Implementation
Handles AI requests and responses using FastAPI.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import os
import tempfile
import logging
from pathlib import Path
import uvicorn

from excel_processor import ExcelProcessor
from google_ai_client import GoogleAIClient

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Excel AI Summarizer MCP Server",
    description="MCP Server for processing Excel files and generating AI summaries",
    version="1.0.0"
)

# Global AI client
ai_client = None


class SummaryRequest(BaseModel):
    """Request model for summary generation."""
    file_path: str
    custom_prompt: Optional[str] = None
    max_tokens: int = 4000
    temperature: float = 0.7
    max_rows_per_sheet: int = 100


class AnalysisRequest(BaseModel):
    """Request model for custom analysis."""
    file_path: str
    analysis_type: str
    specific_questions: Optional[List[str]] = None
    max_rows_per_sheet: int = 100


class SummaryResponse(BaseModel):
    """Response model for summary generation."""
    success: bool
    summary: Optional[str] = None
    error: Optional[str] = None
    file_info: Optional[Dict[str, Any]] = None


@app.on_event("startup")
async def startup_event():
    """Initialize the AI client on startup."""
    global ai_client
    try:
        ai_client = GoogleAIClient()
        logger.info("MCP Server started successfully")
    except Exception as e:
        logger.error(f"Failed to initialize AI client: {str(e)}")
        ai_client = None


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Excel AI Summarizer MCP Server", "status": "running"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    ai_status = "connected" if ai_client and ai_client.test_connection() else "disconnected"
    return {
        "status": "healthy",
        "ai_client": ai_status,
        "timestamp": str(pd.Timestamp.now())
    }


@app.post("/upload-and-summarize", response_model=SummaryResponse)
async def upload_and_summarize(
    file: UploadFile = File(...),
    custom_prompt: Optional[str] = Form(None),
    max_tokens: int = Form(4000),
    temperature: float = Form(0.7),
    max_rows_per_sheet: int = Form(100)
):
    """
    Upload an Excel file and generate a summary.
    """
    if not ai_client:
        raise HTTPException(status_code=500, detail="AI client not initialized")
    
    # Validate file type
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="Only Excel files (.xlsx, .xls) are supported")
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # Process the Excel file
        processor = ExcelProcessor(temp_file_path)
        excel_data = processor.convert_to_text_format(max_rows_per_sheet)
        
        # Generate summary
        summary = ai_client.generate_summary(
            excel_data=excel_data,
            custom_prompt=custom_prompt,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        # Get file info
        file_info = {
            "filename": file.filename,
            "sheets": processor.sheet_names,
            "total_sheets": len(processor.sheet_names)
        }
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        return SummaryResponse(
            success=True,
            summary=summary,
            file_info=file_info
        )
        
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        # Clean up temporary file if it exists
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        
        return SummaryResponse(
            success=False,
            error=str(e)
        )


@app.post("/summarize-local-file", response_model=SummaryResponse)
async def summarize_local_file(request: SummaryRequest):
    """
    Generate a summary for a local Excel file.
    """
    if not ai_client:
        raise HTTPException(status_code=500, detail="AI client not initialized")
    
    try:
        # Check if file exists
        if not Path(request.file_path).exists():
            raise HTTPException(status_code=404, detail=f"File not found: {request.file_path}")
        
        # Process the Excel file
        processor = ExcelProcessor(request.file_path)
        excel_data = processor.convert_to_text_format(request.max_rows_per_sheet)
        
        # Generate summary
        summary = ai_client.generate_summary(
            excel_data=excel_data,
            custom_prompt=request.custom_prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # Get file info
        file_info = {
            "filename": Path(request.file_path).name,
            "sheets": processor.sheet_names,
            "total_sheets": len(processor.sheet_names)
        }
        
        return SummaryResponse(
            success=True,
            summary=summary,
            file_info=file_info
        )
        
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        return SummaryResponse(
            success=False,
            error=str(e)
        )


@app.post("/custom-analysis")
async def custom_analysis(request: AnalysisRequest):
    """
    Generate a custom analysis for an Excel file.
    """
    if not ai_client:
        raise HTTPException(status_code=500, detail="AI client not initialized")
    
    try:
        # Check if file exists
        if not Path(request.file_path).exists():
            raise HTTPException(status_code=404, detail=f"File not found: {request.file_path}")
        
        # Process the Excel file
        processor = ExcelProcessor(request.file_path)
        excel_data = processor.convert_to_text_format(request.max_rows_per_sheet)
        
        # Generate custom analysis
        analysis = ai_client.generate_custom_analysis(
            excel_data=excel_data,
            analysis_type=request.analysis_type,
            specific_questions=request.specific_questions
        )
        
        return {
            "success": True,
            "analysis": analysis,
            "analysis_type": request.analysis_type,
            "file_info": {
                "filename": Path(request.file_path).name,
                "sheets": processor.sheet_names,
                "total_sheets": len(processor.sheet_names)
            }
        }
        
    except Exception as e:
        logger.error(f"Error generating analysis: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@app.get("/file-info/{file_path:path}")
async def get_file_info(file_path: str):
    """
    Get information about an Excel file without processing it.
    """
    try:
        if not Path(file_path).exists():
            raise HTTPException(status_code=404, detail=f"File not found: {file_path}")
        
        processor = ExcelProcessor(file_path)
        processor.read_excel_file()
        
        return {
            "success": True,
            "file_info": {
                "filename": Path(file_path).name,
                "sheets": processor.sheet_names,
                "total_sheets": len(processor.sheet_names),
                "sheet_summaries": processor.get_all_sheets_summary()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting file info: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


def start_server(host: str = "localhost", port: int = 8000):
    """
    Start the MCP server.
    
    Args:
        host (str): Host to bind to
        port (int): Port to bind to
    """
    uvicorn.run(app, host=host, port=port)


if __name__ == "__main__":
    import pandas as pd
    start_server()
