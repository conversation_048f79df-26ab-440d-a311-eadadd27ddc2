"""
Test script to demonstrate the Excel AI Summarizer workflow
This script tests the Excel processing without requiring an API key.
"""

import os
from excel_processor import ExcelProcessor
from pathlib import Path


def test_excel_processing():
    """Test the Excel processing functionality."""
    print("Testing Excel AI Summarizer Workflow")
    print("=" * 50)
    
    # Test with the existing Excel file
    excel_file = "aws-inventory.xlsx"
    
    if not Path(excel_file).exists():
        print(f"Error: Excel file '{excel_file}' not found")
        return False
    
    try:
        # Initialize processor
        print(f"1. Initializing Excel processor for: {excel_file}")
        processor = ExcelProcessor(excel_file)
        
        # Read the Excel file
        print("2. Reading Excel file...")
        data = processor.read_excel_file()
        print(f"   ✓ Successfully read {len(processor.sheet_names)} sheets")
        print(f"   ✓ Sheet names: {', '.join(processor.sheet_names[:5])}{'...' if len(processor.sheet_names) > 5 else ''}")
        
        # Get summary information
        print("3. Getting sheet summaries...")
        summaries = processor.get_all_sheets_summary()
        
        for sheet_name in list(summaries.keys())[:3]:  # Show first 3 sheets
            summary = summaries[sheet_name]
            print(f"   ✓ {sheet_name}: {summary['rows']} rows × {summary['columns']} columns")
        
        # Convert to text format (limited for demo)
        print("4. Converting to AI-ready text format...")
        text_data = processor.convert_to_text_format(max_rows_per_sheet=5)  # Limit for demo
        
        # Show a sample of the text data
        print("5. Sample of processed data:")
        print("-" * 30)
        lines = text_data.split('\n')
        for line in lines[:20]:  # Show first 20 lines
            print(line)
        print("...")
        print(f"   ✓ Total text length: {len(text_data)} characters")
        
        # Save sample output
        output_file = "sample_processed_data.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(text_data)
        print(f"6. Sample data saved to: {output_file}")
        
        print("\n" + "=" * 50)
        print("✓ Excel processing workflow completed successfully!")
        print("\nNext steps:")
        print("Choose one of the following AI services:")
        print("\n🔹 Option 1: Vertex AI (Recommended for production)")
        print("   1. Follow VERTEX_AI_SETUP.md guide")
        print("   2. Set VERTEX_AI_PROJECT_ID in .env file")
        print("   3. Set up GCP authentication")
        print("\n🔹 Option 2: Google AI Studio (Easier for testing)")
        print("   1. Get API key from: https://makersuite.google.com/app/apikey")
        print("   2. Set GOOGLE_API_KEY in .env file")
        print("\nThen run:")
        print("   python main.py --mode interactive")
        print("   python main.py --mode server")
        
        return True
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("\nTesting Configuration...")
    print("-" * 30)

    try:
        from config import get_config_dict

        # Test configuration loading
        print("Configuration structure:")
        config_dict = get_config_dict()

        for section, values in config_dict.items():
            print(f"  {section}:")
            for key, value in values.items():
                print(f"    {key}: {value}")

        # Check AI service configuration
        ai_config = config_dict.get("ai", {})
        if ai_config.get("service") == "Not configured":
            print("\n⚠️  AI service not configured:")
            print("   For Vertex AI: Set VERTEX_AI_PROJECT_ID")
            print("   For AI Studio: Set GOOGLE_API_KEY")
        else:
            print(f"\n✓ AI service configured: {ai_config.get('service')}")

        print("✓ Configuration structure is valid")

    except Exception as e:
        print(f"Configuration test failed: {str(e)}")


def show_usage_examples():
    """Show usage examples."""
    print("\n" + "=" * 50)
    print("USAGE EXAMPLES")
    print("=" * 50)
    
    examples = [
        ("Interactive Mode", "python main.py"),
        ("CLI Mode", "python main.py --mode cli --file aws-inventory.xlsx"),
        ("Server Mode", "python main.py --mode server"),
        ("Custom Prompt", 'python main.py --mode cli --file aws-inventory.xlsx --prompt "Analyze AWS costs"'),
        ("Save Output", "python main.py --mode cli --file aws-inventory.xlsx --output summary.txt"),
    ]
    
    for title, command in examples:
        print(f"\n{title}:")
        print(f"  {command}")
    
    print("\nAPI Endpoints (when running in server mode):")
    endpoints = [
        ("Health Check", "GET /health"),
        ("Upload & Summarize", "POST /upload-and-summarize"),
        ("Local File Summary", "POST /summarize-local-file"),
        ("Custom Analysis", "POST /custom-analysis"),
        ("File Info", "GET /file-info/{file_path}"),
    ]
    
    for title, endpoint in endpoints:
        print(f"  {title}: {endpoint}")


if __name__ == "__main__":
    # Run tests
    success = test_excel_processing()
    test_configuration()
    show_usage_examples()
    
    if success:
        print(f"\n🎉 Setup completed successfully!")
        print("Your Excel AI Summarizer is ready to use!")
    else:
        print(f"\n❌ Setup encountered issues. Please check the errors above.")
