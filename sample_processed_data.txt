EXCEL FILE ANALYSIS: aws-inventory.xlsx
==================================================
Total Sheets: 19
Sheet Names: ************, BACKUP, CERTIFICATES, EBS, EC2, ECR, ECS, EFS, EVENTBRIDGE, GLUE_DB, IAM, KMS, LAMBDA, ROUTE53, ROUTES, S3, SECRETS, SNS, VPC

SHEET: ************
------------------------------
Dimensions: 18 rows × 6 columns
Columns: Service, Global, eu-west-2, us-east-1, us-east-2, Total

Sample Data:
     Service  Global  eu-west-2  us-east-1  us-east-2  Total
      BACKUP       0         56          0          0     56
CERTIFICATES       0          8          0          0      8
         EBS       0         13          0          0     13
         EC2       0         11          0          0     11
         ECR       0         11          0          0     11


SHEET: BACKUP
------------------------------
Dimensions: 56 rows × 19 columns
Columns: AccountId, Region, BackupJobId, BackupVaultName, ResourceType, ResourceName, BackupVaultArn, RecoveryPointArn, ResourceArn, CreationDate, CompletionDate, StartBy, BackupSize, IamRoleArn, State, BackupPlanId, BackupPlanArn, BackupPlanVersion, BackupRuleId

Sample Data:
   AccountId    Region                          BackupJobId BackupVaultName ResourceType             ResourceName                                                     BackupVaultArn                                                                          RecoveryPointArn                                                              ResourceArn              CreationDate                   CompletionDate                   StartBy  BackupSize                                                              IamRoleArn     State                         BackupPlanId                                                                          BackupPlanArn                                BackupPlanVersion                         BackupRuleId
************ eu-west-2 342209C5-0F7A-B250-9B76-8D6F47B49616 Rstudio_spatial          EC2 Spatial - RStudio Server arn:aws:backup:eu-west-2:************:backup-vault:Rstudio_spatial                                        arn:aws:ec2:eu-west-2::image/ami-0dff3e6130e4b4a62          arn:aws:ec2:eu-west-2:************:instance/i-058d55ebe611641e4 2025-07-07 01:00:00+00:00 2025-07-07 03:20:17.216000+00:00 2025-07-07 04:00:00+00:00      6250.0 arn:aws:iam::************:role/service-role/AWSBackupDefaultServiceRole COMPLETED e2a6a5d3-0927-4e35-aba3-c14260158aaf arn:aws:backup:eu-west-2:************:backup-plan:e2a6a5d3-0927-4e35-aba3-c14260158aaf NDZjYzMwYjItYTc4NC00ZjQ3LWIxNWQtYzMxMGQ2NjNmM2Fh f2284e65-b294-44c0-aa71-99f56cf32467
************ eu-west-2 8D7BB11F-55F6-F385-F891-C34FF0FFCDEB Rstudio_spatial          EC2 Spatial - RStudio Server arn:aws:backup:eu-west-2:************:backup-vault:Rstudio_spatial                                        arn:aws:ec2:eu-west-2::image/ami-0b32b9ffb4156b906          arn:aws:ec2:eu-west-2:************:instance/i-058d55ebe611641e4 2025-07-06 01:00:00+00:00 2025-07-06 03:03:36.106000+00:00 2025-07-06 04:00:00+00:00      6250.0 arn:aws:iam::************:role/service-role/AWSBackupDefaultServiceRole COMPLETED e2a6a5d3-0927-4e35-aba3-c14260158aaf arn:aws:backup:eu-west-2:************:backup-plan:e2a6a5d3-0927-4e35-aba3-c14260158aaf NDZjYzMwYjItYTc4NC00ZjQ3LWIxNWQtYzMxMGQ2NjNmM2Fh f2284e65-b294-44c0-aa71-99f56cf32467
************ eu-west-2 84C0B410-2540-A554-7A05-60FB3FF04CBA  RStudio_Server          EC2    RStudio Server Pro v3  arn:aws:backup:eu-west-2:************:backup-vault:RStudio_Server                                        arn:aws:ec2:eu-west-2::image/ami-0f624ed025d2643e0          arn:aws:ec2:eu-west-2:************:instance/i-00bb4ef49af6e07d2 2025-07-05 05:00:00+00:00 2025-07-05 08:29:03.957000+00:00 2025-07-05 13:00:00+00:00      2250.0 arn:aws:iam::************:role/service-role/AWSBackupDefaultServiceRole COMPLETED 81752912-9aaa-49f6-9594-600fd764538c arn:aws:backup:eu-west-2:************:backup-plan:81752912-9aaa-49f6-9594-600fd764538c MzI5ZWIwZDMtNTQyZi00MzZmLWFkODYtZTlmNDdlZTc2ZjBi 3cbf4c51-967f-4638-a0d6-59a86189fdfa
************ eu-west-2 6A8CB545-B94F-C398-8C21-2EE664942EA4     EFS_backups          EFS                      NaN     arn:aws:backup:eu-west-2:************:backup-vault:EFS_backups arn:aws:backup:eu-west-2:************:recovery-point:7b3322d3-7fac-4dac-8eb0-fc89359551fe arn:aws:elasticfilesystem:eu-west-2:************:file-system/fs-7983a988 2025-07-05 05:00:00+00:00 2025-07-05 07:32:44.520000+00:00 2025-07-05 13:00:00+00:00         0.0 arn:aws:iam::************:role/service-role/AWSBackupDefaultServiceRole COMPLETED d7c582a2-6abf-4ca4-88d3-b4991e1c7435 arn:aws:backup:eu-west-2:************:backup-plan:d7c582a2-6abf-4ca4-88d3-b4991e1c7435 NWI5NmZiY2QtYTZlYi00ZWJmLTlkYTUtYWE4ODEzNTA3MTJi 7f40cbed-35ec-4fa9-b0a8-98b042e47955
************ eu-west-2 07CDAA3B-CD2F-9421-2D58-852CC19474D9 Rstudio_spatial          EC2 Spatial - RStudio Server arn:aws:backup:eu-west-2:************:backup-vault:Rstudio_spatial                                        arn:aws:ec2:eu-west-2::image/ami-0bffd3537cc3bd8b0          arn:aws:ec2:eu-west-2:************:instance/i-058d55ebe611641e4 2025-07-05 01:00:00+00:00 2025-07-05 03:14:18.742000+00:00 2025-07-05 04:00:00+00:00      6250.0 arn:aws:iam::************:role/service-role/AWSBackupDefaultServiceRole COMPLETED e2a6a5d3-0927-4e35-aba3-c14260158aaf arn:aws:backup:eu-west-2:************:backup-plan:e2a6a5d3-0927-4e35-aba3-c14260158aaf NDZjYzMwYjItYTc4NC00ZjQ3LWIxNWQtYzMxMGQ2NjNmM2Fh f2284e65-b294-44c0-aa71-99f56cf32467


SHEET: CERTIFICATES
------------------------------
Dimensions: 8 rows × 16 columns
Columns: AccountId, Region, DomainName, SANS, Status, Type, KeyAlgorithm, Usage, InUse, CretedAt, RenewalEligibility, IssuedAt, ImportedAt, Exported, RevokedAt, CertificateARN

Sample Data:
   AccountId    Region               DomainName                     SANS  Status     Type KeyAlgorithm                                                              Usage  InUse            CretedAt RenewalEligibility  IssuedAt                       ImportedAt  Exported  RevokedAt                                                                      CertificateARN
************ eu-west-2       client1.domain.tld       client1.domain.tld EXPIRED IMPORTED     RSA-2048                   DIGITAL_SIGNATURE\nTLS_WEB_CLIENT_AUTHENTICATION  False 2020-12-03 08:59:53         INELIGIBLE       NaN        2020-12-03 08:59:53+00:00       NaN        NaN arn:aws:acm:eu-west-2:************:certificate/dcb8e98e-30d3-4907-a4ea-0f5e2c263b90
************ eu-west-2    clienttry1.domain.tld    clienttry1.domain.tld EXPIRED IMPORTED     RSA-2048                   DIGITAL_SIGNATURE\nTLS_WEB_CLIENT_AUTHENTICATION  False 2020-12-06 13:18:40         INELIGIBLE       NaN        2020-12-06 13:18:40+00:00       NaN        NaN arn:aws:acm:eu-west-2:************:certificate/395c8e5e-81f8-4a83-9c5b-458afaa903ee
************ eu-west-2                   server                   server  ISSUED IMPORTED     RSA-2048 DIGITAL_SIGNATURE\nKEY_ENCIPHERMENT\nTLS_WEB_SERVER_AUTHENTICATION   True 2023-04-20 11:41:00         INELIGIBLE       NaN 2023-04-20 11:41:00.854000+00:00       NaN        NaN arn:aws:acm:eu-west-2:************:certificate/e6eb6378-a6e6-41bc-8e50-a99b086c1be7
************ eu-west-2         vpn.mestagtx.com         vpn.mestagtx.com  ISSUED IMPORTED     RSA-2048 DIGITAL_SIGNATURE\nKEY_ENCIPHERMENT\nTLS_WEB_SERVER_AUTHENTICATION   True 2021-01-06 12:27:38         INELIGIBLE       NaN 2023-04-20 12:31:13.796000+00:00       NaN        NaN arn:aws:acm:eu-west-2:************:certificate/d893b506-584b-4ef1-ba9d-c0d500f99d34
************ eu-west-2 mestag.client.domain.tld mestag.client.domain.tld  ISSUED IMPORTED     RSA-2048                   DIGITAL_SIGNATURE\nTLS_WEB_CLIENT_AUTHENTICATION  False 2023-04-24 14:12:05         INELIGIBLE       NaN 2023-04-24 14:12:05.422000+00:00       NaN        NaN arn:aws:acm:eu-west-2:************:certificate/fc86c051-5ca0-41e9-abef-7a8bf51973f2


SHEET: EBS
------------------------------
Dimensions: 13 rows × 14 columns
Columns: AccountId, Region, VolumeId, Size, State, AvailabilityZone, Type, Iops, CreationTime, SnapshotId, Attached, InsatnceId, Name, Software

Sample Data:
   AccountId    Region              VolumeId  Size  State AvailabilityZone Type   Iops        CreationTime             SnapshotId Attached          InsatnceId                          Name                                      Software
************ eu-west-2 vol-03ba6bf6bb067aab9  2000 in-use       eu-west-2a  gp2 6000.0 2022-06-16 11:44:52 snap-0f1a4d75238c3f133      Yes i-058d55ebe611641e4  Spatial - RStudio Server SSD                                           NaN
************ eu-west-2 vol-0deed03a1b5980cc5    80 in-use       eu-west-2b  gp2  240.0 2022-02-02 16:06:44 snap-06a5a1b884a44f0b9      Yes i-0ab54914d025347bc                           NaN                   Vizgen\nName : GPU instance
************ eu-west-2 vol-09fa563aced7f5397   250 in-use       eu-west-2a  gp2  750.0 2021-04-27 11:44:30 snap-0c737dded4cfdccf5      Yes i-00bb4ef49af6e07d2                           NaN RStudio Server\nName : RStudio Server v3 root
************ eu-west-2 vol-09393c26f4f87ceee   250 in-use       eu-west-2a  gp2  750.0 2022-06-16 11:44:52 snap-088affae1e8b8d7ca      Yes i-058d55ebe611641e4 Spatial - RStudio Server root                                           NaN
************ eu-west-2 vol-09ebd7841b98280bf  4000 in-use       eu-west-2a  st1    NaN 2022-06-16 11:44:52 snap-0aa4cf61cac066ff9      Yes i-058d55ebe611641e4  Spatial - RStudio Server HDD                                           NaN


SHEET: EC2
------------------------------
Dimensions: 11 rows × 27 columns
Columns: AccountId, Region, Name, Instance Id, Instance Type, CPU Cores, Memory, Availability Zone, Private Ip, Public Ip, CreatedAt, State, Subnet Id, Vpc Id, Platform, PlatformDetails, Security Groups, Volumes, VolumeId, size, Total Size, Type, Iops, Snapshot, Software, aws:ec2launchtemplate:id, aws:ec2launchtemplate:version

Sample Data:
   AccountId    Region                     Name         Instance Id Instance Type  CPU Cores  Memory Availability Zone    Private Ip     Public Ip           CreatedAt   State                Subnet Id                Vpc Id Platform PlatformDetails         Security Groups                         Volumes                                                              VolumeId              size  Total Size            Type          Iops                                                                 Snapshot       Software aws:ec2launchtemplate:id  aws:ec2launchtemplate:version
************ eu-west-2    RStudio Server Pro v3 i-00bb4ef49af6e07d2    r5.4xlarge         16   128.0        eu-west-2a *************           NaN 2023-03-16 10:53:02 stopped subnet-0f76a723d2c79fb33 vpc-0fd55a44ce02cc3c5      NaN      Linux/UNIX                 RStudio           /dev/sda1\n/dev/sdb\n                        vol-09fa563aced7f5397\nvol-08da59c1c00065ad9\n       250\n2000\n        2250      gp2\ngp2\n   750\n6000\n                         snap-0c737dded4cfdccf5\nsnap-06e2c3f31ab693ad4\n RStudio Server                      NaN                            NaN
************ eu-west-2     OmicSoft ArrayServer i-0d0866594d3a45e0f     r5.xlarge          4    32.0        eu-west-2a  10.82.10.192 18.132.116.20 2022-05-25 17:45:42 stopped subnet-08b7605ea7dea9493 vpc-0fd55a44ce02cc3c5      NaN      Linux/UNIX OmicSoft security group                             NaN                                                                   NaN               NaN           0             NaN           NaN                                                                      NaN       OmicSoft                      NaN                            NaN
************ eu-west-2 Spatial - RStudio Server i-058d55ebe611641e4   r5.16xlarge         64   512.0        eu-west-2a 10.82.100.252           NaN 2023-08-14 11:39:16 running subnet-0f76a723d2c79fb33 vpc-0fd55a44ce02cc3c5      NaN      Linux/UNIX                 RStudio /dev/sda1\n/dev/sdb\n/dev/sdf\n vol-09393c26f4f87ceee\nvol-03ba6bf6bb067aab9\nvol-09ebd7841b98280bf\n 250\n2000\n4000\n        6250 gp2\ngp2\nst1\n 750\n6000\n\n snap-088affae1e8b8d7ca\nsnap-0f1a4d75238c3f133\nsnap-0aa4cf61cac066ff9\n RStudio Server                      NaN                            NaN
************ eu-west-2             GPU instance i-0ab54914d025347bc   g4dn.xlarge          4    16.0        eu-west-2b 10.82.198.169           NaN 2022-02-02 16:06:44 stopped subnet-03358b2d939508fe2 vpc-0fd55a44ce02cc3c5  windows         Windows       Windows-instances                     /dev/sda1\n                                               vol-0deed03a1b5980cc5\n              80\n          80           gp2\n         240\n                                                 snap-06a5a1b884a44f0b9\n         Vizgen                      NaN                            NaN
************ eu-west-2                      NaN i-0acc7f8841f4d8ee9      t2.micro          1     1.0        eu-west-2b  10.81.30.108           NaN 2023-04-17 21:08:38 stopped subnet-04f45ab5a5693d5d4 vpc-0abde4306507e4568      NaN      Linux/UNIX         launch-wizard-2                     /dev/sda1\n                                               vol-060e02fdb1cca85f2\n              30\n          30           gp2\n         100\n                                                 snap-0f7fcffa6c33ce44d\n            NaN                      NaN                            NaN


SHEET: ECR
------------------------------
Dimensions: 11 rows × 8 columns
Columns: AccountId, Region, RepositoryName, RegistryId, createdAt, RepositoryArn, RepositoryUri, Scaning

Sample Data:
   AccountId    Region               RepositoryName   RegistryId           createdAt                                                              RepositoryArn                                                             RepositoryUri  Scaning
************ eu-west-2                 rstudio-base ************ 2021-04-12 11:01:40                 arn:aws:ecr:eu-west-2:************:repository/rstudio-base                 ************.dkr.ecr.eu-west-2.amazonaws.com/rstudio-base    False
************ eu-west-2 jupyter-datascience-notebook ************ 2022-06-28 08:16:24 arn:aws:ecr:eu-west-2:************:repository/jupyter-datascience-notebook ************.dkr.ecr.eu-west-2.amazonaws.com/jupyter-datascience-notebook    False
************ eu-west-2               rstudio-server ************ 2021-03-02 14:51:31               arn:aws:ecr:eu-west-2:************:repository/rstudio-server               ************.dkr.ecr.eu-west-2.amazonaws.com/rstudio-server    False
************ eu-west-2               rstudio-mestag ************ 2022-07-04 12:25:27               arn:aws:ecr:eu-west-2:************:repository/rstudio-mestag               ************.dkr.ecr.eu-west-2.amazonaws.com/rstudio-mestag    False
************ eu-west-2               rstudio-seurat ************ 2021-04-12 13:18:53               arn:aws:ecr:eu-west-2:************:repository/rstudio-seurat               ************.dkr.ecr.eu-west-2.amazonaws.com/rstudio-seurat    False


SHEET: ECS
------------------------------
Dimensions: 1 rows × 26 columns
Columns: AccountId, Region, ClusterName, ClusterArn, Status, ContainerInstances, runningTasks, pendingTasks, activeServices, capacityProviders, runningEC2TasksCount, runningFargateTasksCount, pendingEC2TasksCount, pendingFargateTasksCount, runningExternalTasksCount, pendingExternalTasksCount, activeEC2ServiceCount, activeFargateServiceCount, drainingEC2ServiceCount, drainingFargateServiceCount, activeExternalServiceCount, drainingExternalServiceCount, Settings, defaultCapacityProviderStrategy, Attachments, namespace

Sample Data:
   AccountId    Region                                         ClusterName                                                                                     ClusterArn Status  ContainerInstances  runningTasks  pendingTasks  activeServices  capacityProviders  runningEC2TasksCount  runningFargateTasksCount  pendingEC2TasksCount  pendingFargateTasksCount  runningExternalTasksCount  pendingExternalTasksCount  activeEC2ServiceCount  activeFargateServiceCount  drainingEC2ServiceCount  drainingFargateServiceCount  activeExternalServiceCount  drainingExternalServiceCount                     Settings  defaultCapacityProviderStrategy  Attachments  namespace
************ eu-west-2 ondemand_Batch_25650feb-4788-3b4b-9e09-4c3f9fffdb03 arn:aws:ecs:eu-west-2:************:cluster/ondemand_Batch_25650feb-4788-3b4b-9e09-4c3f9fffdb03 ACTIVE                   0             0             0               0                NaN                     0                         0                     0                         0                          0                          0                      0                          0                        0                            0                           0                             0 containerInsights : disabled                              NaN          NaN        NaN


SHEET: EFS
------------------------------
Dimensions: 3 rows × 20 columns
Columns: AccountId, Region, Name, FileSystemId, CreationTime, LifeCycleState, NumberOfMountTargets, SizeInMegaBytes, InStandard, InIA, InArchive, PerformanceMode, ThroughputMode, Encrypted, KmsKeyId, ReplicationOverwriteProtection, FileSystemArn, OwnerId, ManagedByAmazonSageMakerResource, Service

Sample Data:
   AccountId    Region                  Name         FileSystemId        CreationTime LifeCycleState  NumberOfMountTargets  SizeInMegaBytes  InStandard    InIA  InArchive PerformanceMode ThroughputMode  Encrypted                                                                    KmsKeyId ReplicationOverwriteProtection                                                                     FileSystemArn      OwnerId                               ManagedByAmazonSageMakerResource Service
************ eu-west-2 SageMaker File System fs-011f608e7dad55a4f 2022-07-11 14:09:04      available                     1               10          10       0          0  generalPurpose       bursting       True arn:aws:kms:eu-west-2:************:key/c2f3aa05-4298-4d2f-9929-d663dee5f280                        ENABLED arn:aws:elasticfilesystem:eu-west-2:************:file-system/fs-011f608e7dad55a4f ************ arn:aws:sagemaker:eu-west-2:************:domain/d-iuigxa43v5kg     NaN
************ eu-west-2                   NaN          fs-7983a988 2021-01-19 13:55:48      available                     2          7798047        5089 7792958          0  generalPurpose       bursting       True arn:aws:kms:eu-west-2:************:key/c2f3aa05-4298-4d2f-9929-d663dee5f280                        ENABLED          arn:aws:elasticfilesystem:eu-west-2:************:file-system/fs-7983a988 ************ arn:aws:sagemaker:eu-west-2:************:domain/d-3lfiv2rmvakv     EFS
************ us-east-1                   NaN fs-0f411233f4e0df896 2022-03-08 07:39:03      available                     6                5           5       0          0  generalPurpose       bursting       True arn:aws:kms:us-east-1:************:key/00cd4716-2468-43bd-8f83-053b189ea2fd                        ENABLED arn:aws:elasticfilesystem:us-east-1:************:file-system/fs-0f411233f4e0df896 ************ arn:aws:sagemaker:us-east-1:************:domain/d-pjq5ammnrgdg     NaN


SHEET: EVENTBRIDGE
------------------------------
Dimensions: 4 rows × 11 columns
Columns: AccountId, Region, Name, Arn, Description, Eventpattern, State, ScheduleExpression, RoleArn, ManagedBy, EventBusName

Sample Data:
   AccountId    Region                                Name                                                                            Arn                                                                                                                                                  Description                                                                                                                                                                                                                                          Eventpattern   State  ScheduleExpression  RoleArn                 ManagedBy EventBusName
************ eu-west-2              AutoScalingManagedRule              arn:aws:events:eu-west-2:************:rule/AutoScalingManagedRule                                                                                        This rule is used to route Instance notifications to EC2 Auto Scaling                                                                                                                               {"source":["aws.ec2"],"detail-type":["EC2 Instance Rebalance Recommendation","EC2 Spot Instance Interruption Warning"]} ENABLED                 NaN      NaN autoscaling.amazonaws.com      default
************ eu-west-2 sagemaker-test-p-joyyolth0cmu-build arn:aws:events:eu-west-2:************:rule/sagemaker-test-p-joyyolth0cmu-build                                                                                Rule to trigger a deployment when ModelBuild CodeCommit repository is updated  {"detail-type":["CodeCommit Repository State Change"],"resources":["arn:aws:codecommit:eu-west-2:************:sagemaker-test-p-joyyolth0cmu-modelbuild"],"source":["aws.codecommit"],"detail":{"referenceType":["branch"],"referenceName":["main"]}} ENABLED                 NaN      NaN                       NaN      default
************ eu-west-2  sagemaker-test-p-joyyolth0cmu-code  arn:aws:events:eu-west-2:************:rule/sagemaker-test-p-joyyolth0cmu-code                                                                                        Rule to trigger a deployment when CodeCommit is updated with a commit {"detail-type":["CodeCommit Repository State Change"],"resources":["arn:aws:codecommit:eu-west-2:************:sagemaker-test-p-joyyolth0cmu-modeldeploy"],"source":["aws.codecommit"],"detail":{"referenceType":["branch"],"referenceName":["main"]}} ENABLED                 NaN      NaN                       NaN      default
************ eu-west-2 sagemaker-test-p-joyyolth0cmu-model arn:aws:events:eu-west-2:************:rule/sagemaker-test-p-joyyolth0cmu-model Rule to trigger a deployment when SageMaker Model registry is updated with a new model package. For example, a new model package is registered with Registry                                                                                                        {"detail-type":["SageMaker Model Package State Change"],"source":["aws.sagemaker"],"detail":{"ModelPackageGroupName":["test-p-joyyolth0cmu"]}} ENABLED                 NaN      NaN                       NaN      default


SHEET: GLUE_DB
------------------------------
Dimensions: 1 rows × 12 columns
Columns: AccountId, Region, DatabaseName, CreateTime, Description, LocationUri, CatalogId, TargetDatabase.CatalogId, TargetDatabase.DatabaseName, TargetDatabase.Region, FederatedDatabase.Identifier, FederatedDatabase.ConnectionName

Sample Data:
   AccountId    Region DatabaseName                CreateTime     Description  LocationUri    CatalogId  TargetDatabase.CatalogId  TargetDatabase.DatabaseName  TargetDatabase.Region  FederatedDatabase.Identifier  FederatedDatabase.ConnectionName
************ eu-west-2     sampledb 2021-04-26 14:46:43+00:00 Sample database          NaN ************                       NaN                          NaN                    NaN                           NaN                               NaN


SHEET: IAM
------------------------------
Dimensions: 19 rows × 6 columns
Columns: AccountId, Region, Username, UserID, UserARN, Created On

Sample Data:
   AccountId Region               Username                UserID                                               UserARN          Created On
************ Global             10xsupport AIDAY3FBPK3RYBHRQKGT4             arn:aws:iam::************:user/10xsupport 2023-08-22 13:49:09
************ Global          Administrator AIDAY3FBPK3RRETHT6XQ7          arn:aws:iam::************:user/Administrator 2020-07-27 10:00:57
************ Global <EMAIL> AIDAY3FBPK3R66QD5LHOE arn:aws:iam::************:user/<EMAIL> 2023-03-24 13:24:21
************ Global            arrayserver AIDAY3FBPK3RVL5MJGWUE            arn:aws:iam::************:user/arrayserver 2021-06-30 11:20:27
************ Global            AWS_Billing AIDAY3FBPK3R2O4WFT2J5            arn:aws:iam::************:user/AWS_Billing 2021-06-09 15:41:08


SHEET: KMS
------------------------------
Dimensions: 27 rows × 22 columns
Columns: AccountId, Region, KeyId, CreationDate, Enabled, Description, KeyUsage, KeyState, Origin, KeyManager, CustomerMasterKeySpec, KeySpec, EncryptionAlgorithms, MacAlgorithms, SigningAlgorithms, MultiRegion, MultiRegionKeyType, CustomKeyStoreId, CloudHsmClusterId, ValidTo, PendingDeletionWindowInDays, XksKeyConfiguration

Sample Data:
   AccountId    Region                                KeyId        CreationDate  Enabled                                                                       Description        KeyUsage KeyState  Origin KeyManager CustomerMasterKeySpec           KeySpec EncryptionAlgorithms  MacAlgorithms  SigningAlgorithms  MultiRegion  MultiRegionKeyType  CustomKeyStoreId  CloudHsmClusterId  ValidTo  PendingDeletionWindowInDays  XksKeyConfiguration
************ eu-west-2 1cd8990f-142e-4d91-8812-b952b91e9a80 2022-04-05 12:23:43     True              Default key that protects my S3 objects when no other key is defined ENCRYPT_DECRYPT  Enabled AWS_KMS        AWS     SYMMETRIC_DEFAULT SYMMETRIC_DEFAULT    SYMMETRIC_DEFAULT            NaN                NaN        False                 NaN               NaN                NaN      NaN                          NaN                  NaN
************ eu-west-2 3650c872-2cf7-4d0a-b7a6-e95097043331 2022-04-05 12:23:14     True Default key that protects my CodeCommit repositories when no other key is defined ENCRYPT_DECRYPT  Enabled AWS_KMS        AWS     SYMMETRIC_DEFAULT SYMMETRIC_DEFAULT    SYMMETRIC_DEFAULT            NaN                NaN        False                 NaN               NaN                NaN      NaN                          NaN                  NaN
************ eu-west-2 3a4930ea-277d-4dd1-87ed-6197bdeb3a59 2021-01-19 13:53:09     True                                                                               NaN ENCRYPT_DECRYPT  Enabled AWS_KMS   CUSTOMER     SYMMETRIC_DEFAULT SYMMETRIC_DEFAULT    SYMMETRIC_DEFAULT            NaN                NaN        False                 NaN               NaN                NaN      NaN                          NaN                  NaN
************ eu-west-2 3a8a7087-0bf1-4f88-a705-ea308b4da9c5 2021-06-01 08:31:19     True      Default master key that protects my EBS volumes when no other key is defined ENCRYPT_DECRYPT  Enabled AWS_KMS        AWS     SYMMETRIC_DEFAULT SYMMETRIC_DEFAULT    SYMMETRIC_DEFAULT            NaN                NaN        False                 NaN               NaN                NaN      NaN                          NaN                  NaN
************ eu-west-2 ********-1091-4607-978c-c83bfa66b2a3 2022-06-23 13:31:44     True                                    Encryption of shared SageMaker notebooks on s3 ENCRYPT_DECRYPT  Enabled AWS_KMS   CUSTOMER     SYMMETRIC_DEFAULT SYMMETRIC_DEFAULT    SYMMETRIC_DEFAULT            NaN                NaN        False                 NaN               NaN                NaN      NaN                          NaN                  NaN


SHEET: LAMBDA
------------------------------
Dimensions: 2 rows × 19 columns
Columns: AccountId, Region, Name, Description, RunTime, Handler, Memory, EphemeralStorage, Timeout, Function ARN, Role ARN, LastModified, State, StateReasonCode, Environment, aws:cloudformation:logical-id, aws:cloudformation:stack-id, aws:cloudformation:stack-name, lambda-console:blueprint

Sample Data:
   AccountId    Region                                                            Name                    Description   RunTime                        Handler  Memory  EphemeralStorage  Timeout                                                                                                   Function ARN                                                                                        Role ARN        LastModified  State  StateReasonCode  Environment  aws:cloudformation:logical-id                                                                                                               aws:cloudformation:stack-id                    aws:cloudformation:stack-name lambda-console:blueprint
************ eu-west-2 BiotechBlueprint-rClientV-rCreateClientVPNEndpoint-5FLX207XBI9S                            NaN python3.6              index.handlerShim    1024               512      240 arn:aws:lambda:eu-west-2:************:function:BiotechBlueprint-rClientV-rCreateClientVPNEndpoint-5FLX207XBI9S arn:aws:iam::************:role/BiotechBlueprint-rClientV-rCreateClientVpnEndpoint-1TJDNRJW3ZO1G 2020-12-03 08:59:33    NaN              NaN          NaN rCreateClientVPNEndpointLambda arn:aws:cloudformation:eu-west-2:************:stack/BiotechBlueprint-rClientVpnEndpoint-NI96BHCN1WMK/bfca8bf0-3545-11eb-8eec-026b7d262768 BiotechBlueprint-rClientVpnEndpoint-NI96BHCN1WMK                      NaN
************ us-east-1                                              hello-world-python A starter AWS Lambda function. python3.7 lambda_function.lambda_handler     128               512        3                                              arn:aws:lambda:us-east-1:************:function:hello-world-python                              arn:aws:iam::************:role/service-role/lambda_basic_execution 2020-10-15 13:32:28    NaN              NaN          NaN                            NaN                                                                                                                                       NaN                                              NaN       hello-world-python


SHEET: ROUTE53
------------------------------
Dimensions: 1 rows × 10 columns
Columns: AccountId, Region, Id, Name, CallerReference, Comment, PrivateZone, ResourceRecordSetCount, linkedServicePrincipal, ServiceDescription

Sample Data:
   AccountId Region                                Id          Name                           CallerReference  Comment  PrivateZone  ResourceRecordSetCount  linkedServicePrincipal  ServiceDescription
************ Global /hostedzone/Z10031582TZD97H5LNBWD mestagtx.com. rInternalHostedZoneForPortCo-ya3nKPqotCpj      NaN         True                       5                     NaN                 NaN


SHEET: ROUTES
------------------------------
Dimensions: 26 rows × 12 columns
Columns: AccountId, Region, RouteTableName, RouteTableId, VpcId, Routes:DestinationCidrBlock, Routes:GatewayId, Routes:State, Association:Subnets, Association:SId, Association:Gateways, Association:GId

Sample Data:
   AccountId     Region                                 RouteTableName          RouteTableId                 VpcId                Routes:DestinationCidrBlock      Routes:GatewayId     Routes:State        Association:Subnets              Association:SId Association:Gateways Association:GId
************ ap-south-1                                        Default          rtb-ee09a585          vpc-2931df42                 **********/16\n0.0.0.0/0\n local\nigw-e2f9428a\n active\nactive\n                         \n                           \n                  NaN             NaN
************ eu-north-1                                        Default          rtb-0a03b263          vpc-46d5642f                 **********/16\n0.0.0.0/0\n local\nigw-cd982ca4\n active\nactive\n                         \n                           \n                  NaN             NaN
************  eu-west-3                                        Default          rtb-54906e3c          vpc-8229c7ea                 **********/16\n0.0.0.0/0\n local\nigw-a4c0edcd\n active\nactive\n                         \n                           \n                  NaN             NaN
************  eu-west-2                                        Private rtb-05851f1cbe0f98f8b vpc-0abde4306507e4568 10.81.0.0/16\n10.82.0.0/16\n,\n0.0.0.0/0\n               local\n         active\n subnet-04f45ab5a5693d5d4\n rtbassoc-0f7c3f6dda473be20\n                  ,\n             ,\n
************  eu-west-2 BiotechBlueprint-rPreclinicalVpc-1TQ6AFJMV10PR rtb-0d8b787ab3de02086 vpc-0fd55a44ce02cc3c5 10.81.0.0/16\n,\n10.82.0.0/16\n0.0.0.0/0\n            ,\nlocal\n      ,\nactive\n subnet-0f76a723d2c79fb33\n rtbassoc-09f3956125c000cb0\n                  ,\n             ,\n


SHEET: S3
------------------------------
Dimensions: 50 rows × 18 columns
Columns: AccountId, Region, Name, CreatedTime, Lifecycle, aws:cloudformation:stack-id, aws:cloudformation:stack-name, aws:cloudformation:logical-id, Project, Collaborator, aws:servicecatalog:productArn, sagemaker:project-name, aws:servicecatalog:provisioningPrincipalArn, aws:servicecatalog:provisioningArtifactIdentifier, sagemaker:project-id, aws:servicecatalog:portfolioArn, aws:servicecatalog:provisionedProductArn, Test

Sample Data:
   AccountId Region                                                            Name         CreatedTime                                                                                                                                      Lifecycle                                                                                                               aws:cloudformation:stack-id                    aws:cloudformation:stack-name aws:cloudformation:logical-id Project Collaborator aws:servicecatalog:productArn sagemaker:project-name aws:servicecatalog:provisioningPrincipalArn aws:servicecatalog:provisioningArtifactIdentifier sagemaker:project-id aws:servicecatalog:portfolioArn aws:servicecatalog:provisionedProductArn Test
************ Global                                                        10xshare 2023-08-22 13:42:21                                                                                                                                            NaN                                                                                                                                       NaN                                              NaN                           NaN     NaN          NaN                           NaN                    NaN                                         NaN                                               NaN                  NaN                             NaN                                      NaN  NaN
************ Global  biotechblueprint-rclientvpnendpo-rvpnconfigbucket-rooxrxqzgz52 2020-12-03 08:58:50                                                                                                                                            NaN arn:aws:cloudformation:eu-west-2:************:stack/BiotechBlueprint-rClientVpnEndpoint-NI96BHCN1WMK/bfca8bf0-3545-11eb-8eec-026b7d262768 BiotechBlueprint-rClientVpnEndpoint-NI96BHCN1WMK              rVpnConfigBucket     NaN          NaN                           NaN                    NaN                                         NaN                                               NaN                  NaN                             NaN                                      NaN  NaN
************ Global biotechblueprint-rlogging-18rz-rarchivelogsbucket-1lvq430g8kw2d 2020-12-03 08:47:39 {'Expiration': {'Days': 2555}\n'ID': 'Transition90daysRetain7yrs'\n'Status': 'Enabled'\n'Transition': {'Days': 90\n'StorageClass': 'GLACIER'}}          arn:aws:cloudformation:eu-west-2:************:stack/BiotechBlueprint-rLogging-18RZ43GHH69DW/2fa123f0-3544-11eb-8bc5-02ab5a3e1414          BiotechBlueprint-rLogging-18RZ43GHH69DW            rArchiveLogsBucket     NaN          NaN                           NaN                    NaN                                         NaN                                               NaN                  NaN                             NaN                                      NaN  NaN
************ Global  biotechblueprint-rlogging-18rz4-rcloudtrailbucket-bxw8worzrpmb 2020-12-03 08:48:03                                                                                                                                            NaN          arn:aws:cloudformation:eu-west-2:************:stack/BiotechBlueprint-rLogging-18RZ43GHH69DW/2fa123f0-3544-11eb-8bc5-02ab5a3e1414          BiotechBlueprint-rLogging-18RZ43GHH69DW             rCloudTrailBucket     NaN          NaN                           NaN                    NaN                                         NaN                                               NaN                  NaN                             NaN                                      NaN  NaN
************ Global                             cf-templates-t586hu8sxh5h-eu-west-2 2020-12-03 08:43:15                                                                                                                                            NaN          arn:aws:cloudformation:eu-west-2:************:stack/BiotechBlueprint-rLogging-18RZ43GHH69DW/2fa123f0-3544-11eb-8bc5-02ab5a3e1414          BiotechBlueprint-rLogging-18RZ43GHH69DW             rCloudTrailBucket     NaN          NaN                           NaN                    NaN                                         NaN                                               NaN                  NaN                             NaN                                      NaN  NaN


SHEET: SECRETS
------------------------------
Dimensions: 4 rows × 13 columns
Columns: AccountId, Region, SecretARN, Name, Description, CreatedOn, KMSKeyId, Rotation, RotationRule, LastRotatedDate, LastChangedDate, LastUsed, OwningService

Sample Data:
   AccountId    Region                                                                                    SecretARN                             Name         Description  CreatedOn  KMSKeyId  Rotation  RotationRule  LastRotatedDate     LastChangedDate            LastUsed  OwningService
************ eu-west-2           arn:aws:secretsmanager:eu-west-2:************:secret:BB/vpn/ca_key_password-xHeqTC           BB/vpn/ca_key_password VPN CA key password        NaN       NaN       NaN           NaN              NaN 2020-12-29 14:39:51 2023-04-24 00:00:00            NaN
************ eu-west-2       arn:aws:secretsmanager:eu-west-2:************:secret:BB/vpn/server_key_password-W8NY8f       BB/vpn/server_key_password    vpn.mestagtx.com        NaN       NaN       NaN           NaN              NaN 2025-04-27 03:11:28 2021-03-11 00:00:00            NaN
************ eu-west-2       arn:aws:secretsmanager:eu-west-2:************:secret:BB/vpn/user_cert_passwords-oZmM4O       BB/vpn/user_cert_passwords                 NaN        NaN       NaN       NaN           NaN              NaN 2025-04-27 03:38:17 2021-03-11 00:00:00            NaN
************ eu-west-2 arn:aws:secretsmanager:eu-west-2:************:secret:AmazonSageMaker-sagemaker-github-jbsV4J AmazonSageMaker-sagemaker-github                 NaN        NaN       NaN       NaN           NaN              NaN 2025-04-27 13:25:47                 NaN            NaN


SHEET: SNS
------------------------------
Dimensions: 3 rows × 6 columns
Columns: AccountId, Region, Endpoint, Protocol, SubscriptionArn, TopicArn

Sample Data:
   AccountId    Region           Endpoint Protocol                                                                                                                                   SubscriptionArn                                                                                                     TopicArn
************ eu-west-2 <EMAIL>    email                                                       arn:aws:sns:eu-west-2:************:RStudio-Server-down:6319028b-5ae6-48b2-b972-05865f7eb3aa                                                       arn:aws:sns:eu-west-2:************:RStudio-Server-down
************ eu-west-2 <EMAIL>    email arn:aws:sns:eu-west-2:************:BiotechBlueprint-rLogging-18RZ43GHH69DW-rSecurityAlarmTopic-105EXG8FXCKSM:4ec40a5d-9ff0-42ff-9610-5d2cb90af38f arn:aws:sns:eu-west-2:************:BiotechBlueprint-rLogging-18RZ43GHH69DW-rSecurityAlarmTopic-105EXG8FXCKSM
************ us-east-1 <EMAIL>    email                                                              arn:aws:sns:us-east-1:************:BillingAlert:a640f8ad-1360-4e6f-93fc-f01eedc4ae7a                                                              arn:aws:sns:us-east-1:************:BillingAlert


SHEET: VPC
------------------------------
Dimensions: 18 rows × 10 columns
Columns: AccountId, Region, VpcID, CIDRBlock, State, IsDefault, Subnets, SubnetCIDR, SubnetZone, SubnetCIDR.1

Sample Data:
   AccountId     Region                 VpcID     CIDRBlock     State  IsDefault                                                                                                                                                    Subnets                                                                                  SubnetCIDR                                                             SubnetZone                                                                                SubnetCIDR.1
************ ap-south-1          vpc-2931df42 **********/16 available       True                                                                                                          subnet-a6f4b1ea\nsubnet-361e095e\nsubnet-3873fc43                                               **********/20\n172.31.32.0/20\n172.31.16.0/20                                  ap-south-1b\nap-south-1a\nap-south-1c                                               **********/20\n172.31.32.0/20\n172.31.16.0/20
************ eu-north-1          vpc-46d5642f **********/16 available       True                                                                                                          subnet-6261b719\nsubnet-3c3d8a55\nsubnet-a227d7ef                                               172.31.32.0/20\n172.31.16.0/20\n**********/20                                  eu-north-1b\neu-north-1a\neu-north-1c                                               172.31.32.0/20\n172.31.16.0/20\n**********/20
************  eu-west-3          vpc-8229c7ea **********/16 available       True                                                                                                          subnet-d7f1e4be\nsubnet-0ab39f71\nsubnet-e080f1ad                                               **********/20\n172.31.16.0/20\n172.31.32.0/20                                     eu-west-3a\neu-west-3b\neu-west-3c                                               **********/20\n172.31.16.0/20\n172.31.32.0/20
************  eu-west-2 vpc-0fd55a44ce02cc3c5  10.82.0.0/16 available      False subnet-08d6c8e280cbb0ca5\nsubnet-03358b2d939508fe2\nsubnet-08fb1e5b1e5b1a877\nsubnet-0083a6c25c431b54f\nsubnet-08b7605ea7dea9493\nsubnet-0f76a723d2c79fb33 10.82.20.0/24\n10.82.192.0/21\n10.82.208.0/21\n10.82.112.0/21\n10.82.10.0/24\n10.82.96.0/21 eu-west-2b\neu-west-2b\neu-west-2b\neu-west-2a\neu-west-2a\neu-west-2a 10.82.20.0/24\n10.82.192.0/21\n10.82.208.0/21\n10.82.112.0/21\n10.82.10.0/24\n10.82.96.0/21
************  eu-west-2 vpc-0abde4306507e4568  10.81.0.0/16 available      False                                                   subnet-04f45ab5a5693d5d4\nsubnet-098ccd6911a24c9e1\nsubnet-070f8633240adc88a\nsubnet-07e2bdbd21082d71a\n                                  10.81.30.0/24\n10.81.2.0/24\n10.81.20.0/24\n10.81.1.0/24\n                       eu-west-2b\neu-west-2b\neu-west-2a\neu-west-2a\n                                  10.81.30.0/24\n10.81.2.0/24\n10.81.20.0/24\n10.81.1.0/24\n

