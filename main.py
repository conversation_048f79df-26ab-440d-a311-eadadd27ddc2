"""
Main Application for Excel AI Summarizer
Provides both CLI and server interfaces for processing Excel files and generating AI summaries.
"""

import argparse
import sys
import os
from pathlib import Path
import logging
from typing import Optional
import asyncio

from excel_processor import ExcelProcessor
from google_ai_client import GoogleAIClient
from mcp_server import start_server
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ExcelAISummarizer:
    """Main application class for Excel AI Summarizer."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the summarizer.
        
        Args:
            api_key (Optional[str]): Google AI API key
        """
        self.ai_client = None
        try:
            self.ai_client = GoogleAIClient(api_key=api_key)
            logger.info("AI client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize AI client: {str(e)}")
            raise
    
    def process_file(self, 
                    file_path: str, 
                    custom_prompt: Optional[str] = None,
                    max_rows_per_sheet: int = 100,
                    output_file: Optional[str] = None) -> str:
        """
        Process an Excel file and generate a summary.
        
        Args:
            file_path (str): Path to the Excel file
            custom_prompt (Optional[str]): Custom prompt for AI
            max_rows_per_sheet (int): Maximum rows to process per sheet
            output_file (Optional[str]): Path to save the summary
            
        Returns:
            str: Generated summary
        """
        try:
            logger.info(f"Processing Excel file: {file_path}")
            
            # Process Excel file
            processor = ExcelProcessor(file_path)
            excel_data = processor.convert_to_text_format(max_rows_per_sheet)
            
            logger.info(f"Found {len(processor.sheet_names)} sheets: {processor.sheet_names}")
            
            # Generate summary
            summary = self.ai_client.generate_summary(
                excel_data=excel_data,
                custom_prompt=custom_prompt
            )
            
            # Save to file if specified
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"Excel File Summary: {Path(file_path).name}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(summary)
                logger.info(f"Summary saved to: {output_file}")
            
            return summary
            
        except Exception as e:
            logger.error(f"Error processing file: {str(e)}")
            raise
    
    def interactive_mode(self):
        """Run the application in interactive mode."""
        print("Excel AI Summarizer - Interactive Mode")
        print("=" * 40)
        
        while True:
            try:
                # Get file path
                file_path = input("\nEnter Excel file path (or 'quit' to exit): ").strip()
                
                if file_path.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye!")
                    break
                
                if not Path(file_path).exists():
                    print(f"Error: File not found - {file_path}")
                    continue
                
                # Get custom prompt
                custom_prompt = input("Enter custom prompt (or press Enter for default): ").strip()
                if not custom_prompt:
                    custom_prompt = None
                
                # Get max rows
                max_rows_input = input("Max rows per sheet (default 100): ").strip()
                max_rows = int(max_rows_input) if max_rows_input.isdigit() else 100
                
                # Ask about saving
                save_output = input("Save summary to file? (y/n): ").strip().lower()
                output_file = None
                if save_output == 'y':
                    output_file = input("Enter output file path: ").strip()
                    if not output_file:
                        output_file = f"{Path(file_path).stem}_summary.txt"
                
                print("\nProcessing...")
                summary = self.process_file(file_path, custom_prompt, max_rows, output_file)
                
                print("\n" + "=" * 50)
                print("SUMMARY:")
                print("=" * 50)
                print(summary)
                print("=" * 50)
                
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error: {str(e)}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Excel AI Summarizer")
    parser.add_argument("--mode", choices=["cli", "server", "interactive"], default="interactive",
                       help="Run mode: cli, server, or interactive")
    parser.add_argument("--file", type=str, help="Excel file path (for CLI mode)")
    parser.add_argument("--prompt", type=str, help="Custom prompt for AI")
    parser.add_argument("--output", type=str, help="Output file path")
    parser.add_argument("--max-rows", type=int, default=100, help="Max rows per sheet")
    parser.add_argument("--host", type=str, default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--api-key", type=str, help="Google AI API key")
    
    args = parser.parse_args()
    
    # Check for API key
    api_key = args.api_key or os.getenv("GOOGLE_API_KEY")
    if not api_key and args.mode != "server":
        print("Error: Google AI API key is required.")
        print("Set GOOGLE_API_KEY environment variable or use --api-key argument.")
        sys.exit(1)
    
    try:
        if args.mode == "server":
            print(f"Starting MCP server on {args.host}:{args.port}")
            start_server(host=args.host, port=args.port)
            
        elif args.mode == "cli":
            if not args.file:
                print("Error: --file argument is required for CLI mode")
                sys.exit(1)
            
            summarizer = ExcelAISummarizer(api_key=api_key)
            summary = summarizer.process_file(
                file_path=args.file,
                custom_prompt=args.prompt,
                max_rows_per_sheet=args.max_rows,
                output_file=args.output
            )
            
            print("\n" + "=" * 50)
            print("SUMMARY:")
            print("=" * 50)
            print(summary)
            
        elif args.mode == "interactive":
            summarizer = ExcelAISummarizer(api_key=api_key)
            summarizer.interactive_mode()
            
    except Exception as e:
        logger.error(f"Application error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
