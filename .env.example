# Official Vertex AI Configuration (Recommended)
# The client gets the API key from the environment variable GEMINI_API_KEY
GEMINI_API_KEY=your_gemini_api_key_here
VERTEX_AI_MODEL=gemini-2.5-flash

# Alternative: Google AI Studio (if you prefer this over Vertex AI)
# GOOGLE_API_KEY=your_google_ai_api_key_here

# Legacy Vertex AI Configuration (if using old approach)
# VERTEX_AI_PROJECT_ID=your_gcp_project_id_here
# VERTEX_AI_LOCATION=us-central1
# GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# MCP Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8000

# Application Configuration
DEFAULT_PROMPT="Please provide a comprehensive summary of the data from all sheets in this Excel file, highlighting key insights, trends, and important information."
MAX_TOKENS=4000
TEMPERATURE=0.7
