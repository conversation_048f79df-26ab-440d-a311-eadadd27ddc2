#!/bin/bash

# Excel AI Summarizer Startup Script

echo "🚀 Excel AI Summarizer"
echo "======================"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  No .env file found. Creating from template..."
    cp .env.example .env
    echo "📝 Please edit .env file and add your Google AI API key:"
    echo "   GOOGLE_API_KEY=your_google_ai_api_key_here"
    echo ""
fi

# Check if Google API key is set
if ! grep -q "GOOGLE_API_KEY=your_google_ai_api_key_here" .env && grep -q "GOOGLE_API_KEY=" .env; then
    echo "✅ Google AI API key found in .env"
else
    echo "⚠️  Please set your Google AI API key in .env file"
    echo "   Get your key from: https://makersuite.google.com/app/apikey"
    echo ""
fi

# Show menu
echo "Choose an option:"
echo "1. Interactive Mode (recommended for first use)"
echo "2. Start MCP Server"
echo "3. Process current Excel file (CLI)"
echo "4. Test setup"
echo "5. Exit"
echo ""

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo "🔄 Starting interactive mode..."
        python3 main.py --mode interactive
        ;;
    2)
        echo "🌐 Starting MCP server on localhost:8000..."
        echo "   Access at: http://localhost:8000"
        echo "   Health check: http://localhost:8000/health"
        echo "   Press Ctrl+C to stop"
        python3 main.py --mode server
        ;;
    3)
        if [ -f "aws-inventory.xlsx" ]; then
            echo "📊 Processing aws-inventory.xlsx..."
            python3 main.py --mode cli --file aws-inventory.xlsx --output aws-summary.txt
            echo "✅ Summary saved to aws-summary.txt"
        else
            echo "❌ aws-inventory.xlsx not found"
            echo "   Place your Excel file in this directory and try again"
        fi
        ;;
    4)
        echo "🧪 Running setup test..."
        python3 test_workflow.py
        ;;
    5)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac
