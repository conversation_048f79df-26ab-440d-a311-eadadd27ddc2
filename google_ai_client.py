"""
Google AI Integration Module
Handles communication with Google's Generative AI API and Vertex AI for generating summaries.
"""

import os
from typing import Optional, Dict, Any
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import both Vertex AI and Google AI Studio
try:
    import vertexai
    from vertexai.generative_models import GenerativeModel
    VERTEX_AI_AVAILABLE = True
    logger.info("Vertex AI SDK available")
except ImportError:
    VERTEX_AI_AVAILABLE = False
    logger.warning("Vertex AI SDK not available")

try:
    import google.generativeai as genai
    GOOGLE_AI_AVAILABLE = True
    logger.info("Google AI Studio SDK available")
except ImportError:
    GOOGLE_AI_AVAILABLE = False
    logger.warning("Google AI Studio SDK not available")


class GoogleAIClient:
    """Client for interacting with Google's Generative AI API and Vertex AI."""

    def __init__(self,
                 api_key: Optional[str] = None,
                 model_name: str = "gemini-1.5-flash",
                 use_vertex_ai: Optional[bool] = None,
                 project_id: Optional[str] = None,
                 location: Optional[str] = None):
        """
        Initialize the Google AI client.

        Args:
            api_key (Optional[str]): Google AI API key for AI Studio. If None, will try to get from environment.
            model_name (str): Name of the model to use
            use_vertex_ai (Optional[bool]): Whether to use Vertex AI. If None, auto-detect based on environment.
            project_id (Optional[str]): GCP project ID for Vertex AI
            location (Optional[str]): GCP location for Vertex AI
        """
        self.model_name = model_name
        self.use_vertex_ai = use_vertex_ai
        self.project_id = project_id or os.getenv("VERTEX_AI_PROJECT_ID")
        self.location = location or os.getenv("VERTEX_AI_LOCATION", "us-central1")
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")

        # Auto-detect which service to use
        if self.use_vertex_ai is None:
            self.use_vertex_ai = bool(self.project_id and VERTEX_AI_AVAILABLE)

        # Initialize the appropriate client
        if self.use_vertex_ai:
            self._init_vertex_ai()
        else:
            self._init_google_ai_studio()

    def _init_vertex_ai(self):
        """Initialize Vertex AI client."""
        if not VERTEX_AI_AVAILABLE:
            raise ImportError("Vertex AI SDK not available. Install with: pip install google-cloud-aiplatform")

        if not self.project_id:
            raise ValueError("Project ID is required for Vertex AI. Set VERTEX_AI_PROJECT_ID environment variable.")

        try:
            # Initialize Vertex AI
            vertexai.init(project=self.project_id, location=self.location)
            self.model = GenerativeModel(self.model_name)
            logger.info(f"Initialized Vertex AI client with model: {self.model_name} in {self.project_id}/{self.location}")
        except Exception as e:
            logger.error(f"Failed to initialize Vertex AI model: {str(e)}")
            raise

    def _init_google_ai_studio(self):
        """Initialize Google AI Studio client."""
        if not GOOGLE_AI_AVAILABLE:
            raise ImportError("Google AI Studio SDK not available. Install with: pip install google-generativeai")

        if not self.api_key:
            raise ValueError("Google AI API key is required for AI Studio. Set GOOGLE_API_KEY environment variable.")

        try:
            # Configure the API
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)
            logger.info(f"Initialized Google AI Studio client with model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Google AI Studio model: {str(e)}")
            raise
    
    def generate_summary(self,
                        excel_data: str,
                        custom_prompt: Optional[str] = None,
                        max_tokens: int = 4000,
                        temperature: float = 0.7) -> str:
        """
        Generate a summary of the Excel data using Google AI.

        Args:
            excel_data (str): Formatted text representation of Excel data
            custom_prompt (Optional[str]): Custom prompt for the AI. If None, uses default.
            max_tokens (int): Maximum number of tokens in the response
            temperature (float): Temperature for response generation (0.0 to 1.0)

        Returns:
            str: Generated summary
        """
        try:
            # Default prompt if none provided
            if custom_prompt is None:
                custom_prompt = os.getenv("DEFAULT_PROMPT",
                    "Please provide a comprehensive summary of the data from all sheets in this Excel file, "
                    "highlighting key insights, trends, and important information.")

            # Construct the full prompt
            full_prompt = f"""
{custom_prompt}

Here is the Excel file data:

{excel_data}

Please provide a detailed summary that includes:
1. Overview of the data structure and sheets
2. Key insights and patterns found in the data
3. Important statistics or trends
4. Any notable observations or recommendations
"""

            service_name = "Vertex AI" if self.use_vertex_ai else "Google AI Studio"
            logger.info(f"Generating summary with {service_name}...")

            # Generate content with appropriate configuration
            if self.use_vertex_ai:
                # Vertex AI configuration
                from vertexai.generative_models import GenerationConfig
                generation_config = GenerationConfig(
                    max_output_tokens=max_tokens,
                    temperature=temperature,
                )
                response = self.model.generate_content(
                    full_prompt,
                    generation_config=generation_config
                )
            else:
                # Google AI Studio configuration
                response = self.model.generate_content(
                    full_prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=max_tokens,
                        temperature=temperature,
                    )
                )

            if response.text:
                logger.info("Successfully generated summary")
                return response.text
            else:
                logger.warning(f"Empty response from {service_name}")
                return "No summary could be generated."

        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            raise
    
    def generate_custom_analysis(self,
                                excel_data: str,
                                analysis_type: str,
                                specific_questions: Optional[list] = None) -> str:
        """
        Generate a custom analysis based on specific requirements.

        Args:
            excel_data (str): Formatted text representation of Excel data
            analysis_type (str): Type of analysis (e.g., "financial", "inventory", "performance")
            specific_questions (Optional[list]): List of specific questions to answer

        Returns:
            str: Generated analysis
        """
        try:
            # Construct analysis prompt
            prompt = f"""
Please perform a {analysis_type} analysis of the following Excel data.
"""

            if specific_questions:
                prompt += "\nPlease specifically address these questions:\n"
                for i, question in enumerate(specific_questions, 1):
                    prompt += f"{i}. {question}\n"

            prompt += f"""
Here is the Excel file data:

{excel_data}

Please provide a detailed analysis with actionable insights.
"""

            service_name = "Vertex AI" if self.use_vertex_ai else "Google AI Studio"
            logger.info(f"Generating {analysis_type} analysis with {service_name}...")

            response = self.model.generate_content(prompt)

            if response.text:
                logger.info("Successfully generated custom analysis")
                return response.text
            else:
                logger.warning(f"Empty response from {service_name}")
                return "No analysis could be generated."

        except Exception as e:
            logger.error(f"Error generating custom analysis: {str(e)}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test the connection to Google AI API.

        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            test_response = self.model.generate_content("Hello, this is a test.")
            return bool(test_response.text)
        except Exception as e:
            service_name = "Vertex AI" if self.use_vertex_ai else "Google AI Studio"
            logger.error(f"{service_name} connection test failed: {str(e)}")
            return False

    def get_service_info(self) -> Dict[str, Any]:
        """
        Get information about the current AI service configuration.

        Returns:
            Dict[str, Any]: Service information
        """
        return {
            "service": "Vertex AI" if self.use_vertex_ai else "Google AI Studio",
            "model": self.model_name,
            "project_id": self.project_id if self.use_vertex_ai else None,
            "location": self.location if self.use_vertex_ai else None,
            "has_api_key": bool(self.api_key) if not self.use_vertex_ai else None
        }


def create_ai_client(api_key: Optional[str] = None,
                    use_vertex_ai: Optional[bool] = None,
                    project_id: Optional[str] = None,
                    location: Optional[str] = None) -> GoogleAIClient:
    """
    Factory function to create a Google AI client.

    Args:
        api_key (Optional[str]): Google AI API key for AI Studio
        use_vertex_ai (Optional[bool]): Whether to use Vertex AI
        project_id (Optional[str]): GCP project ID for Vertex AI
        location (Optional[str]): GCP location for Vertex AI

    Returns:
        GoogleAIClient: Configured AI client
    """
    return GoogleAIClient(
        api_key=api_key,
        use_vertex_ai=use_vertex_ai,
        project_id=project_id,
        location=location
    )


if __name__ == "__main__":
    # Test the AI client
    try:
        client = GoogleAIClient()
        service_info = client.get_service_info()
        print(f"Using {service_info['service']} with model {service_info['model']}")

        if client.test_connection():
            print("AI client connection successful!")
        else:
            print("Failed to connect to AI service")
    except Exception as e:
        print(f"Error testing AI client: {e}")
