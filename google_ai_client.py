"""
Google AI Integration Module
Handles communication with Google's Generative AI API for generating summaries.
"""

import google.generativeai as genai
import os
from typing import Optional, Dict, Any
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GoogleAIClient:
    """Client for interacting with Google's Generative AI API."""
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gemini-1.5-flash"):
        """
        Initialize the Google AI client.
        
        Args:
            api_key (Optional[str]): Google AI API key. If None, will try to get from environment.
            model_name (str): Name of the model to use
        """
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        self.model_name = model_name
        
        if not self.api_key:
            raise ValueError("Google AI API key is required. Set GOOGLE_API_KEY environment variable or pass api_key parameter.")
        
        # Configure the API
        genai.configure(api_key=self.api_key)
        
        # Initialize the model
        try:
            self.model = genai.GenerativeModel(model_name)
            logger.info(f"Initialized Google AI client with model: {model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Google AI model: {str(e)}")
            raise
    
    def generate_summary(self, 
                        excel_data: str, 
                        custom_prompt: Optional[str] = None,
                        max_tokens: int = 4000,
                        temperature: float = 0.7) -> str:
        """
        Generate a summary of the Excel data using Google AI.
        
        Args:
            excel_data (str): Formatted text representation of Excel data
            custom_prompt (Optional[str]): Custom prompt for the AI. If None, uses default.
            max_tokens (int): Maximum number of tokens in the response
            temperature (float): Temperature for response generation (0.0 to 1.0)
            
        Returns:
            str: Generated summary
        """
        try:
            # Default prompt if none provided
            if custom_prompt is None:
                custom_prompt = os.getenv("DEFAULT_PROMPT", 
                    "Please provide a comprehensive summary of the data from all sheets in this Excel file, "
                    "highlighting key insights, trends, and important information.")
            
            # Construct the full prompt
            full_prompt = f"""
{custom_prompt}

Here is the Excel file data:

{excel_data}

Please provide a detailed summary that includes:
1. Overview of the data structure and sheets
2. Key insights and patterns found in the data
3. Important statistics or trends
4. Any notable observations or recommendations
"""
            
            logger.info("Generating summary with Google AI...")
            
            # Generate content
            response = self.model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=max_tokens,
                    temperature=temperature,
                )
            )
            
            if response.text:
                logger.info("Successfully generated summary")
                return response.text
            else:
                logger.warning("Empty response from Google AI")
                return "No summary could be generated."
                
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            raise
    
    def generate_custom_analysis(self, 
                                excel_data: str, 
                                analysis_type: str,
                                specific_questions: Optional[list] = None) -> str:
        """
        Generate a custom analysis based on specific requirements.
        
        Args:
            excel_data (str): Formatted text representation of Excel data
            analysis_type (str): Type of analysis (e.g., "financial", "inventory", "performance")
            specific_questions (Optional[list]): List of specific questions to answer
            
        Returns:
            str: Generated analysis
        """
        try:
            # Construct analysis prompt
            prompt = f"""
Please perform a {analysis_type} analysis of the following Excel data.
"""
            
            if specific_questions:
                prompt += "\nPlease specifically address these questions:\n"
                for i, question in enumerate(specific_questions, 1):
                    prompt += f"{i}. {question}\n"
            
            prompt += f"""
Here is the Excel file data:

{excel_data}

Please provide a detailed analysis with actionable insights.
"""
            
            logger.info(f"Generating {analysis_type} analysis with Google AI...")
            
            response = self.model.generate_content(prompt)
            
            if response.text:
                logger.info("Successfully generated custom analysis")
                return response.text
            else:
                logger.warning("Empty response from Google AI")
                return "No analysis could be generated."
                
        except Exception as e:
            logger.error(f"Error generating custom analysis: {str(e)}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test the connection to Google AI API.
        
        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            test_response = self.model.generate_content("Hello, this is a test.")
            return bool(test_response.text)
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return False


def create_ai_client(api_key: Optional[str] = None) -> GoogleAIClient:
    """
    Factory function to create a Google AI client.
    
    Args:
        api_key (Optional[str]): Google AI API key
        
    Returns:
        GoogleAIClient: Configured AI client
    """
    return GoogleAIClient(api_key=api_key)


if __name__ == "__main__":
    # Test the AI client
    try:
        client = GoogleAIClient()
        if client.test_connection():
            print("Google AI client connection successful!")
        else:
            print("Failed to connect to Google AI")
    except Exception as e:
        print(f"Error testing Google AI client: {e}")
